// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'example_app_info_route.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$ExampleAppInfoRoute {
  String get route;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleAppInfoRouteCopyWith<ExampleAppInfoRoute> get copyWith =>
      _$ExampleAppInfoRouteCopyWithImpl<ExampleAppInfoRoute>(
          this as ExampleAppInfoRoute, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleAppInfoRoute &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route);

  @override
  String toString() {
    return 'ExampleAppInfoRoute(route: $route)';
  }
}

///
abstract mixin class $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleAppInfoRouteCopyWith(
          ExampleAppInfoRoute value, $Res Function(ExampleAppInfoRoute) _then) =
      _$ExampleAppInfoRouteCopyWithImpl;
  @useResult
  $Res call({String route});
}

///
class _$ExampleAppInfoRouteCopyWithImpl<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  _$ExampleAppInfoRouteCopyWithImpl(this._self, this._then);

  final ExampleAppInfoRoute _self;
  final $Res Function(ExampleAppInfoRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? route = null,
  }) {
    return _then(_self.copyWith(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

///

class ExampleInitialRoute extends ExampleAppInfoRoute
    implements _ExampleAppInfoRouteDefaultParams {
  const ExampleInitialRoute({this.route = kExampleInitial}) : super._();

  @override
  @JsonKey()
  final String route;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleInitialRouteCopyWith<ExampleInitialRoute> get copyWith =>
      _$ExampleInitialRouteCopyWithImpl<ExampleInitialRoute>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleInitialRoute &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route);

  @override
  String toString() {
    return 'ExampleAppInfoRoute.initial(route: $route)';
  }
}

///
abstract mixin class $ExampleInitialRouteCopyWith<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleInitialRouteCopyWith(
          ExampleInitialRoute value, $Res Function(ExampleInitialRoute) _then) =
      _$ExampleInitialRouteCopyWithImpl;
  @override
  @useResult
  $Res call({String route});
}

///
class _$ExampleInitialRouteCopyWithImpl<$Res>
    implements $ExampleInitialRouteCopyWith<$Res> {
  _$ExampleInitialRouteCopyWithImpl(this._self, this._then);

  final ExampleInitialRoute _self;
  final $Res Function(ExampleInitialRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
  }) {
    return _then(ExampleInitialRoute(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

///

class ExampleLoginRoute extends ExampleAppInfoRoute
    implements _ExampleAppInfoRouteDefaultParams {
  const ExampleLoginRoute({this.route = kExampleLogin}) : super._();

  @override
  @JsonKey()
  final String route;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleLoginRouteCopyWith<ExampleLoginRoute> get copyWith =>
      _$ExampleLoginRouteCopyWithImpl<ExampleLoginRoute>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleLoginRoute &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route);

  @override
  String toString() {
    return 'ExampleAppInfoRoute.login(route: $route)';
  }
}

///
abstract mixin class $ExampleLoginRouteCopyWith<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleLoginRouteCopyWith(
          ExampleLoginRoute value, $Res Function(ExampleLoginRoute) _then) =
      _$ExampleLoginRouteCopyWithImpl;
  @override
  @useResult
  $Res call({String route});
}

///
class _$ExampleLoginRouteCopyWithImpl<$Res>
    implements $ExampleLoginRouteCopyWith<$Res> {
  _$ExampleLoginRouteCopyWithImpl(this._self, this._then);

  final ExampleLoginRoute _self;
  final $Res Function(ExampleLoginRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
  }) {
    return _then(ExampleLoginRoute(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

///

class ExampleHomeRoute extends ExampleAppInfoRoute
    implements _ExampleAppInfoRouteDefaultParams {
  const ExampleHomeRoute({this.route = kExampleHome}) : super._();

  @override
  @JsonKey()
  final String route;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleHomeRouteCopyWith<ExampleHomeRoute> get copyWith =>
      _$ExampleHomeRouteCopyWithImpl<ExampleHomeRoute>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleHomeRoute &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route);

  @override
  String toString() {
    return 'ExampleAppInfoRoute.home(route: $route)';
  }
}

///
abstract mixin class $ExampleHomeRouteCopyWith<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleHomeRouteCopyWith(
          ExampleHomeRoute value, $Res Function(ExampleHomeRoute) _then) =
      _$ExampleHomeRouteCopyWithImpl;
  @override
  @useResult
  $Res call({String route});
}

///
class _$ExampleHomeRouteCopyWithImpl<$Res>
    implements $ExampleHomeRouteCopyWith<$Res> {
  _$ExampleHomeRouteCopyWithImpl(this._self, this._then);

  final ExampleHomeRoute _self;
  final $Res Function(ExampleHomeRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
  }) {
    return _then(ExampleHomeRoute(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

///

class ExampleUserRoute extends ExampleAppInfoRoute
    implements _ExampleAppInfoRouteDefaultParams {
  const ExampleUserRoute({this.route = kExampleUserDetails, required this.user})
      : super._();

  @override
  @JsonKey()
  final String route;
  final User user;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleUserRouteCopyWith<ExampleUserRoute> get copyWith =>
      _$ExampleUserRouteCopyWithImpl<ExampleUserRoute>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleUserRoute &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route, user);

  @override
  String toString() {
    return 'ExampleAppInfoRoute.userProfile(route: $route, user: $user)';
  }
}

///
abstract mixin class $ExampleUserRouteCopyWith<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleUserRouteCopyWith(
          ExampleUserRoute value, $Res Function(ExampleUserRoute) _then) =
      _$ExampleUserRouteCopyWithImpl;
  @override
  @useResult
  $Res call({String route, User user});
}

///
class _$ExampleUserRouteCopyWithImpl<$Res>
    implements $ExampleUserRouteCopyWith<$Res> {
  _$ExampleUserRouteCopyWithImpl(this._self, this._then);

  final ExampleUserRoute _self;
  final $Res Function(ExampleUserRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
    Object? user = null,
  }) {
    return _then(ExampleUserRoute(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      user: null == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }
}

///

class ExampleAssigneeRoute extends ExampleAppInfoRoute
    implements _ExampleAppInfoRouteDefaultParams {
  const ExampleAssigneeRoute(
      {this.route = kExampleAssigneeDetails, required this.entity})
      : super._();

  @override
  @JsonKey()
  final String route;
  final AssigneeEntity entity;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ExampleAssigneeRouteCopyWith<ExampleAssigneeRoute> get copyWith =>
      _$ExampleAssigneeRouteCopyWithImpl<ExampleAssigneeRoute>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ExampleAssigneeRoute &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.entity, entity) || other.entity == entity));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route, entity);

  @override
  String toString() {
    return 'ExampleAppInfoRoute.assigneeDetails(route: $route, entity: $entity)';
  }
}

///
abstract mixin class $ExampleAssigneeRouteCopyWith<$Res>
    implements $ExampleAppInfoRouteCopyWith<$Res> {
  factory $ExampleAssigneeRouteCopyWith(ExampleAssigneeRoute value,
          $Res Function(ExampleAssigneeRoute) _then) =
      _$ExampleAssigneeRouteCopyWithImpl;
  @override
  @useResult
  $Res call({String route, AssigneeEntity entity});

  $AssigneeEntityCopyWith<$Res> get entity;
}

///
class _$ExampleAssigneeRouteCopyWithImpl<$Res>
    implements $ExampleAssigneeRouteCopyWith<$Res> {
  _$ExampleAssigneeRouteCopyWithImpl(this._self, this._then);

  final ExampleAssigneeRoute _self;
  final $Res Function(ExampleAssigneeRoute) _then;

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? route = null,
    Object? entity = null,
  }) {
    return _then(ExampleAssigneeRoute(
      route: null == route
          ? _self.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
      entity: null == entity
          ? _self.entity
          : entity // ignore: cast_nullable_to_non_nullable
              as AssigneeEntity,
    ));
  }

  /// Create a copy of ExampleAppInfoRoute
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AssigneeEntityCopyWith<$Res> get entity {
    return $AssigneeEntityCopyWith<$Res>(_self.entity, (value) {
      return _then(_self.copyWith(entity: value));
    });
  }
}

// dart format on
