import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/presentation/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';

import '../../di/modules/app_module_test.dart';

// Test widget that implements HomeBehaviorMixin
class HomeBehaviorWidget extends StatelessWidget with HomeBehaviorMixin {
  const HomeBehaviorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CommonBloc>(
      create: (context) => GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
      child: RepositoryProvider.value(
        value: GetIt.I<GPAppNavigator<ExampleAppInfoRoute>>(
            instanceName: 'kAppNavigator'),
        child: const SizedBox(),
      ),
    );
  }
}

void main() {
  group('HomeBehavior', () {
    Widget createTestWidget({Widget? child}) {
      return MaterialApp(
        home: Scaffold(
          body: child ?? const HomeBehaviorWidget(),
        ),
      );
    }

    group('Navigation', () {
      group('navigateToUserProfile', () {
        testWidgets('should navigate to user profile page', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.navigateToUserProfile(context);
          await tester.pumpAndSettle();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context correctly', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.navigateToUserProfile(context), returnsNormally);
        });

        testWidgets('should create user with correct data', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // The method creates a User with specific data
          // This test verifies the method executes without error
          expect(() => widget.navigateToUserProfile(context), returnsNormally);
        });
      });
    });

    group('SnackBar Methods', () {
      group('showNormalSnackBar', () {
        testWidgets('should show normal snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showNormalSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showNormalSnackBar(context: context),
              returnsNormally);
        });
      });

      group('showSuccessSnackBar', () {
        testWidgets('should show success snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showSuccessSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showSuccessSnackBar(context: context),
              returnsNormally);
        });
      });

      group('showErrorSnackBar', () {
        testWidgets('should show error snackbar', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showErrorSnackBar(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showErrorSnackBar(context: context),
              returnsNormally);
        });
      });
    });

    group('Dialog Methods', () {
      group('showDialogWithOneBtn', () {
        testWidgets('should show dialog with one button', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showDialogWithOneBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithOneBtn(context: context),
              returnsNormally);
        });
      });

      group('showDialogWithTwoBtn', () {
        testWidgets('should show dialog with two buttons', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showDialogWithTwoBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithTwoBtn(context: context),
              returnsNormally);
        });
      });

      group('showDialogWithTwoVerticalBtn', () {
        testWidgets('should show dialog with two vertical buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showDialogWithTwoVerticalBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showDialogWithTwoVerticalBtn(context: context),
              returnsNormally);
        });
      });
    });

    group('BottomSheet Methods', () {
      group('showSimpleBottomSheet', () {
        testWidgets('should show simple bottom sheet', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showSimpleBottomSheet(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showSimpleBottomSheet(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithOneBtn', () {
        testWidgets('should show bottom sheet with one button', (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showBottomSheetWithOneBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showBottomSheetWithOneBtn(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithTwoBtn', () {
        testWidgets('should show bottom sheet with two buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showBottomSheetWithTwoBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(() => widget.showBottomSheetWithTwoBtn(context: context),
              returnsNormally);
        });
      });

      group('showBottomSheetWithTwoVerticalBtn', () {
        testWidgets('should show bottom sheet with two vertical buttons',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Act
          widget.showBottomSheetWithTwoVerticalBtn(context: context);
          await tester.pump();

          // Verify method completes successfully
          expect(find.byType(HomeBehaviorWidget), findsOneWidget);
        });

        testWidgets('should handle context parameter correctly',
            (tester) async {
          await tester.pumpWidget(createTestWidget());

          final widget = tester.widget<HomeBehaviorWidget>(
            find.byType(HomeBehaviorWidget),
          );
          final context = tester.element(find.byType(HomeBehaviorWidget));

          // Should not throw when called with valid context
          expect(
              () => widget.showBottomSheetWithTwoVerticalBtn(context: context),
              returnsNormally);
        });
      });
    });

    group('Behavior Interface Compliance', () {
      test('HomeBehaviorWidget should implement HomeBehavior', () {
        const widget = HomeBehaviorWidget();
        expect(widget, isA<HomeBehavior>());
      });

      test('HomeBehaviorMixin should provide all required methods', () {
        const widget = HomeBehaviorWidget();

        // Verify all methods exist and are callable
        expect(widget.navigateToUserProfile, isA<Function>());
        expect(widget.showNormalSnackBar, isA<Function>());
        expect(widget.showSuccessSnackBar, isA<Function>());
        expect(widget.showErrorSnackBar, isA<Function>());
        expect(widget.showDialogWithOneBtn, isA<Function>());
        expect(widget.showDialogWithTwoBtn, isA<Function>());
        expect(widget.showDialogWithTwoVerticalBtn, isA<Function>());
        expect(widget.showSimpleBottomSheet, isA<Function>());
        expect(widget.showBottomSheetWithOneBtn, isA<Function>());
        expect(widget.showBottomSheetWithTwoBtn, isA<Function>());
        expect(widget.showBottomSheetWithTwoVerticalBtn, isA<Function>());
      });
    });

    group('Error Handling', () {
      testWidgets('should handle invalid context gracefully', (tester) async {
        const widget = HomeBehaviorWidget();

        // Create a detached context
        await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        final context = tester.element(find.byType(Scaffold));

        // These calls might throw due to missing navigator context, which is expected behavior
        expect(() => widget.navigateToUserProfile(context), throwsA(anything));
        expect(() => widget.showNormalSnackBar(context: context),
            throwsA(anything));
      });
    });
  });
}
