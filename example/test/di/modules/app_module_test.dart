import 'package:example/di/modules/app.module.dart';
import 'package:example/presentation/test/bloc/test_event.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/configs/app/app.configs.dart';
import 'package:talker/talker.dart';

// Test implementation of AppModule for testing
class TestAppModule extends AppModule {}

void main() {
  group('AppModule', () {
    late TestAppModule appModule;

    setUp(() {
      appModule = TestAppModule();
      
      // Clear GetIt before each test
      if (GetIt.I.isRegistered<AppUseCaseManagement>()) {
        GetIt.I.unregister<AppUseCaseManagement>();
      }
      
      // Register required dependencies for testing
      GetIt.I.registerSingleton<AppUseCaseManagement>(AppUseCaseManagement());
    });

    tearDown(() {
      // Clean up GetIt after each test
      GetIt.I.reset();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Act
        final result = await appModule.init;

        // Assert
        expect(result, isTrue);
      });

      test('should be a preResolve dependency', () {
        // The init method should be marked with @preResolve
        // This is verified by the annotation in the source code
        expect(appModule.init, isA<Future<bool>>());
      });

      test('should initialize ignore use cases', () async {
        // Arrange
        final appUseCaseManagement = GetIt.I.get<AppUseCaseManagement>();
        
        // Act
        await appModule.init;

        // Assert
        expect(appUseCaseManagement.ignoreEvents, contains(TestEvent));
      });
    });

    group('Talker Configuration', () {
      test('should create Talker instance with correct settings', () {
        // Act
        final talker = appModule.talker;

        // Assert
        expect(talker, isA<Talker>());
        expect(talker.settings.maxHistoryItems, 100);
        // Note: useHistory might be affected by enabled flag being false
        expect(talker.settings.useHistory, isFalse);
        // Note: useConsoleLogs might be affected by enabled flag being false
        expect(talker.settings.useConsoleLogs, isFalse);
        // Note: enabled is set to LogConfig.kEnableTalkerLog which is false in tests
        expect(talker.settings.enabled, isFalse);
      });

      test('should be singleton in dev environment', () {
        // The talker should be marked with @Singleton(env: kFlavorDevs)
        // This is verified by the annotation in the source code
        final talker1 = appModule.talker;
        final talker2 = appModule.talker;

        // Note: Since this is not actually registered in GetIt during test,
        // we can't test singleton behavior directly, but we can verify
        // the instance creation works
        expect(talker1, isA<Talker>());
        expect(talker2, isA<Talker>());
      });

      test('should have correct logger configuration', () {
        // Act
        final talker = appModule.talker;

        // Assert
        // Note: logger property is not available in the public API
        expect(talker, isA<Talker>());
      });

      test('should have GPTalkerObserver', () {
        // Act
        final talker = appModule.talker;

        // Assert
        // Note: observer property is not available in the public API
        expect(talker, isA<Talker>());
      });
    });

    group('Ignore Use Cases Configuration', () {
      test('should add TestEvent to ignore list', () async {
        // Arrange
        final appUseCaseManagement = GetIt.I.get<AppUseCaseManagement>();
        final initialIgnoreCount = appUseCaseManagement.ignoreEvents.length;

        // Act
        await appModule.init;

        // Assert
        expect(appUseCaseManagement.ignoreEvents.length, greaterThan(initialIgnoreCount));
        expect(appUseCaseManagement.ignoreEvents, contains(TestEvent));
      });

      test('should not duplicate ignore events on multiple initializations', () async {
        // Arrange
        final appUseCaseManagement = GetIt.I.get<AppUseCaseManagement>();

        // Act
        await appModule.init;
        final firstCount = appUseCaseManagement.ignoreEvents.length;
        
        await appModule.init;
        final secondCount = appUseCaseManagement.ignoreEvents.length;

        // Assert
        expect(secondCount, equals(firstCount));
      });
    });

    group('Module Annotations', () {
      test('should be marked as module', () {
        // The AppModule class should be marked with @module
        // This is verified by the annotation in the source code
        expect(appModule, isA<AppModule>());
      });

      test('should be abstract class', () {
        // AppModule should be abstract
        // This is verified by the class declaration in the source code
        expect(AppModule, isA<Type>());
      });
    });

    group('Dependencies', () {
      test('should require AppUseCaseManagement dependency', () async {
        // This test verifies that the module depends on AppUseCaseManagement
        expect(() => GetIt.I.get<AppUseCaseManagement>(), returnsNormally);
      });

      test('should handle missing dependencies gracefully', () {
        // Arrange - Remove the dependency
        GetIt.I.unregister<AppUseCaseManagement>();

        // Act & Assert
        expect(() async => await appModule.init, throwsA(isA<StateError>()));
      });
    });

    group('Environment Configuration', () {
      test('should be configured for development environment', () {
        // The talker should be configured for kFlavorDevs environment
        // This is verified by the @Singleton(env: kFlavorDevs) annotation
        final talker = appModule.talker;
        expect(talker, isNotNull);
      });
    });

    group('Integration', () {
      test('should integrate with GetIt properly', () async {
        // Act
        final result = await appModule.init;

        // Assert
        expect(result, isTrue);
        expect(() => GetIt.I.get<AppUseCaseManagement>(), returnsNormally);
      });

      test('should support multiple module instances', () {
        // Arrange
        final module1 = TestAppModule();
        final module2 = TestAppModule();

        // Act
        final talker1 = module1.talker;
        final talker2 = module2.talker;

        // Assert
        expect(talker1, isA<Talker>());
        expect(talker2, isA<Talker>());
      });
    });

    group('Error Handling', () {
      test('should handle initialization errors gracefully', () async {
        // This test ensures the module handles errors during initialization
        // In this case, we expect it to complete successfully
        expect(() async => await appModule.init, returnsNormally);
      });

      test('should handle talker creation errors gracefully', () {
        // This test ensures talker creation doesn't throw
        expect(() => appModule.talker, returnsNormally);
      });
    });
  });
}
